using Calculators;

namespace Exercise1Tests;

public class Exercise1Tests
{
    [Test]
    public void AdditionTest()
    {
        // Arrange
        var calculator = new SimpleCalculator();

        // Act
        var result = calculator.Enter(4).Plus(2).Equals();

        // Assert
        Assert.That(result, Is.EqualTo(6));
    }

    [Test]
    public void SubtractionTest()
    {
        // Implement this test (Part 1)
        // Enter 4, subtract 2, expect result 2

        Assert.Fail("TODO: Implement this test");
    }

    [Test]
    public void MultiplicationTest()
    {
        // Implement this test (Part 1)
        // Enter 4, multiply by 2, expect result 8

        Assert.Fail("TODO: Implement this test");
    }

    [Test]
    public void DivisionTest()
    {
        // Implement this test (Part 1)
        // Enter 4, divide by 2, expect result 2

        Assert.Fail("TODO: Implement this test");
    }
}