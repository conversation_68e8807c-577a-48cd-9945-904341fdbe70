using Calculators;

namespace Exercise1Tests;

public class Exercise1Tests
{
    [Test]
    public void AdditionTest()
    {
        // Arrange
        var calculator = new SimpleCalculator();

        // Act
        var result = calculator.Enter(4).Plus(2).Equals();

        // Assert
        Assert.That(result, Is.EqualTo(6));
    }

    // TODO: Exercise 1 - Students need to implement these tests

    [Test]
    public void SubtractionTest()
    {
        // TODO: Write a test that enters the number 4 into the calculator, then subtracts 2
        // Expected result: 2

        // Arrange
        // var calculator = new SimpleCalculator();

        // Act
        // var result = calculator.Enter(4).Minus(2).Equals();

        // Assert
        // Assert.That(result, Is.EqualTo(2));

        Assert.Fail("TODO: Implement this test");
    }

    [Test]
    public void MultiplicationTest()
    {
        // TODO: Write a test that enters the number 4 into the calculator, then multiplies it by 2
        // Expected result: 8

        // Arrange
        // var calculator = new SimpleCalculator();

        // Act
        // var result = calculator.Enter(4).MultiplyBy(2).Equals();

        // Assert
        // Assert.That(result, Is.EqualTo(8));

        Assert.Fail("TODO: Implement this test");
    }

    [Test]
    public void DivisionTest()
    {
        // TODO: Write a test that enters the number 4 into the calculator, then divides it by 2
        // Expected result: 2

        // Arrange
        // var calculator = new SimpleCalculator();

        // Act
        // var result = calculator.Enter(4).DivideBy(2).Equals();

        // Assert
        // Assert.That(result, Is.EqualTo(2));

        Assert.Fail("TODO: Implement this test");
    }
}