{"version": 2, "dgSpecHash": "RvoS5Spkd6w=", "success": true, "projectFilePath": "/home/<USER>/WTC/QA-meta/execise-1-calculator/Exercise1Tests/Exercise1Tests.csproj", "expectedPackageFiles": ["/home/<USER>/.nuget/packages/coverlet.collector/6.0.0/coverlet.collector.6.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.codecoverage/17.8.0/microsoft.codecoverage.17.8.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.net.test.sdk/17.8.0/microsoft.net.test.sdk.17.8.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.netcore.platforms/1.1.0/microsoft.netcore.platforms.1.1.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.testplatform.objectmodel/17.8.0/microsoft.testplatform.objectmodel.17.8.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.testplatform.testhost/17.8.0/microsoft.testplatform.testhost.17.8.0.nupkg.sha512", "/home/<USER>/.nuget/packages/netstandard.library/2.0.0/netstandard.library.2.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/newtonsoft.json/13.0.1/newtonsoft.json.13.0.1.nupkg.sha512", "/home/<USER>/.nuget/packages/nuget.frameworks/6.5.0/nuget.frameworks.6.5.0.nupkg.sha512", "/home/<USER>/.nuget/packages/nunit/3.14.0/nunit.3.14.0.nupkg.sha512", "/home/<USER>/.nuget/packages/nunit.analyzers/3.9.0/nunit.analyzers.3.9.0.nupkg.sha512", "/home/<USER>/.nuget/packages/nunit3testadapter/4.5.0/nunit3testadapter.4.5.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.io.pipelines/9.0.9/system.io.pipelines.9.0.9.nupkg.sha512", "/home/<USER>/.nuget/packages/system.net.http.json/9.0.9/system.net.http.json.9.0.9.nupkg.sha512", "/home/<USER>/.nuget/packages/system.reflection.metadata/1.6.0/system.reflection.metadata.1.6.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.text.encodings.web/9.0.9/system.text.encodings.web.9.0.9.nupkg.sha512", "/home/<USER>/.nuget/packages/system.text.json/9.0.9/system.text.json.9.0.9.nupkg.sha512"], "logs": []}