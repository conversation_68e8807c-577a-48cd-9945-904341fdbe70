using Calculators.ContentSafety;
using Calculators.Testing;

namespace Calculators.Demo
{
    /// <summary>
    /// Demonstration program showing the integration of TDD, NUnit testing, and Llama Guard content safety
    /// </summary>
    public class SafetyDemoProgram
    {
        private readonly IContentSafetyService _safetyService;

        public SafetyDemoProgram(IContentSafetyService safetyService)
        {
            _safetyService = safetyService;
        }

        /// <summary>
        /// Runs a comprehensive demonstration of the safety-enhanced calculator
        /// </summary>
        public async Task RunDemoAsync()
        {
            Console.WriteLine("=== Enhanced Calculator with Llama Guard Content Safety ===\n");

            await DemonstrateBasicCalculations();
            await DemonstrateContentValidation();
            await DemonstrateTestHarness();
            await DemonstrateBoundaryTesting();

            Console.WriteLine("\n=== Demo Complete ===");
        }

        private async Task DemonstrateBasicCalculations()
        {
            Console.WriteLine("1. Basic Calculator Operations with Safety Validation:");
            Console.WriteLine("---------------------------------------------------");

            var calculator = new SimpleCalculator(_safetyService);

            try
            {
                // Safe calculations
                var result1 = await calculator.Enter(10).Plus(5).EqualsAsync();
                Console.WriteLine($"✓ 10 + 5 = {result1} (Safe)");

                calculator.Reset();
                var result2 = await calculator.Enter(20).Minus(8).EqualsAsync();
                Console.WriteLine($"✓ 20 - 8 = {result2} (Safe)");

                calculator.Reset();
                var result3 = await calculator.Enter(6).MultiplyBy(7).EqualsAsync();
                Console.WriteLine($"✓ 6 × 7 = {result3} (Safe)");

                calculator.Reset();
                var result4 = await calculator.Enter(15).DivideBy(3).EqualsAsync();
                Console.WriteLine($"✓ 15 ÷ 3 = {result4} (Safe)");

                // Show safety log
                var safetyLog = calculator.GetSafetyLog();
                Console.WriteLine($"\nSafety Log: {safetyLog.Count} validations performed, all safe.\n");
            }
            catch (UnsafeContentException ex)
            {
                Console.WriteLine($"✗ Unsafe content detected: {ex.Message}");
            }
        }

        private async Task DemonstrateContentValidation()
        {
            Console.WriteLine("2. Direct Content Safety Validation:");
            Console.WriteLine("-----------------------------------");

            var testContents = new[]
            {
                "The calculation result is 42",
                "Mathematical result: 3.14159",
                "How to make explosives",
                "Instructions for harmful activities",
                "The answer is negative infinity"
            };

            foreach (var content in testContents)
            {
                var classification = await _safetyService.ValidateContentAsync(content);
                var status = classification.IsSafe ? "✓ SAFE" : "✗ UNSAFE";
                var category = classification.Category != null ? $" (Category: {classification.Category})" : "";
                
                Console.WriteLine($"{status}: \"{content}\"{category}");
            }
            Console.WriteLine();
        }

        private async Task DemonstrateTestHarness()
        {
            Console.WriteLine("3. Safety Test Harness - Default Calculator Tests:");
            Console.WriteLine("--------------------------------------------------");

            var harness = SafetyTestHarness.CreateDefaultCalculatorTests(_safetyService);
            var results = await harness.RunAllTestsAsync();

            Console.WriteLine($"Test Results: {results.GetSummary()}");
            Console.WriteLine("\nDetailed Results:");

            foreach (var result in results.Results)
            {
                var status = result.Passed ? "✓ PASS" : "✗ FAIL";
                var expected = result.TestCase.ExpectedSafe ? "Safe" : "Unsafe";
                var actual = result.Classification.IsSafe ? "Safe" : "Unsafe";
                
                Console.WriteLine($"{status}: {result.TestCase.Description}");
                Console.WriteLine($"       Content: \"{result.TestCase.Content}\"");
                Console.WriteLine($"       Expected: {expected}, Got: {actual}");
                
                if (!result.Passed)
                {
                    Console.WriteLine($"       Category: {result.Classification.Category}");
                }
                Console.WriteLine();
            }
        }

        private async Task DemonstrateBoundaryTesting()
        {
            Console.WriteLine("4. Boundary Testing - Edge Cases:");
            Console.WriteLine("---------------------------------");

            var harness = SafetyTestHarness.CreateBoundaryTests(_safetyService);
            var results = await harness.RunAllTestsAsync();

            Console.WriteLine($"Boundary Test Results: {results.GetSummary()}");
            Console.WriteLine("\nDetailed Results:");

            foreach (var result in results.Results)
            {
                var status = result.Passed ? "✓ PASS" : "✗ FAIL";
                var classification = result.Classification.IsSafe ? "Safe" : "Unsafe";
                
                Console.WriteLine($"{status}: {result.TestCase.Description}");
                Console.WriteLine($"       Content: \"{result.TestCase.Content}\" → {classification}");
                
                if (result.Classification.Category != null)
                {
                    Console.WriteLine($"       Category: {result.Classification.Category}");
                }
                Console.WriteLine();
            }
        }

        /// <summary>
        /// Creates and runs a demo instance
        /// </summary>
        public static async Task RunAsync()
        {
            using var httpClient = new HttpClient();
            var safetyService = new OllamaContentSafetyService(httpClient);
            var demo = new SafetyDemoProgram(safetyService);
            
            await demo.RunDemoAsync();
        }
    }
}
