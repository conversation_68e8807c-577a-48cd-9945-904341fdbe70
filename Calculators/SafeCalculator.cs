using Calculators.ContentSafety;

namespace Calculators
{
    /// <summary>
    /// A calculator that validates outputs for content safety using Llama Guard
    /// </summary>
    public class SafeCalculator
    {
        private readonly SimpleCalculator _calculator;
        private readonly IContentSafetyService _safetyService;
        private readonly List<SafetyClassification> _safetyLog;

        public SafeCalculator(IContentSafetyService safetyService)
        {
            _calculator = new SimpleCalculator();
            _safetyService = safetyService;
            _safetyLog = new List<SafetyClassification>();
        }

        public SafeCalculator Reset()
        {
            _calculator.Reset();
            return this;
        }

        public SafeCalculator Enter(decimal number)
        {
            _calculator.Enter(number);
            return this;
        }

        public SafeCalculator Plus(decimal number)
        {
            _calculator.Plus(number);
            return this;
        }

        public SafeCalculator Minus(decimal number)
        {
            _calculator.Minus(number);
            return this;
        }

        public SafeCalculator MultiplyBy(decimal number)
        {
            _calculator.MultiplyBy(number);
            return this;
        }

        public SafeCalculator DivideBy(decimal number)
        {
            _calculator.DivideBy(number);
            return this;
        }

        /// <summary>
        /// Gets the result and validates it for content safety
        /// </summary>
        /// <returns>The calculation result if safe</returns>
        /// <exception cref="UnsafeContentException">Thrown when content is deemed unsafe</exception>
        public async Task<decimal> EqualsAsync()
        {
            var result = _calculator.Equals();
            var resultText = $"The calculation result is {result}";
            
            var safetyResult = await _safetyService.ValidateContentAsync(resultText);
            _safetyLog.Add(safetyResult);
            
            if (!safetyResult.IsSafe)
            {
                throw new UnsafeContentException($"Content validation failed: {safetyResult.Category}", safetyResult);
            }
            
            return result;
        }

        /// <summary>
        /// Gets the result without safety validation (for testing purposes)
        /// </summary>
        public decimal EqualsUnsafe()
        {
            return _calculator.Equals();
        }

        /// <summary>
        /// Gets the safety log for all operations performed
        /// </summary>
        public IReadOnlyList<SafetyClassification> GetSafetyLog()
        {
            return _safetyLog.AsReadOnly();
        }

        /// <summary>
        /// Validates a custom message for safety
        /// </summary>
        public async Task<SafetyClassification> ValidateMessageAsync(string message)
        {
            var result = await _safetyService.ValidateContentAsync(message);
            _safetyLog.Add(result);
            return result;
        }
    }
}
