using Calculators;
using Calculators.ContentSafety;
using Calculators.Testing;

namespace Exercise2Tests;

public class SafetyIntegrationTests
{
    private IContentSafetyService _safetyService;
    private HttpClient _httpClient;
    private SimpleCalculator _calculator;

    [SetUp]
    public void Setup()
    {
        _httpClient = new HttpClient();
        _safetyService = new OllamaContentSafetyService(_httpClient);
        // Add safety calculator setup here (Part 2)
        // _calculator = new SimpleCalculator(_safetyService);
        // _calculator.Enter(4);
    }

    [TearDown]
    public void TearDown()
    {
        // Add calculator reset here (Part 2)
        // _calculator.Reset();
        _httpClient?.Dispose();
    }

    [Test]
    public Task SimpleCalculator_BasicCalculation_WithSafety_ShouldReturnSafeResult()
    {
        // Implement this test using setup calculator (Part 3)
        // Calculator already has 4, add 2, expect 6 with safety validation
        
        Assert.Fail("TODO: Implement this test");
        return Task.CompletedTask;
    }

    [Test]
    public Task SimpleCalculator_MultipleOperations_ShouldLogAllSafetyChecks()
    {
        // Implement this test using setup/teardown (Part 3)
        // Use setup calculator, perform operations, verify safety logging
        
        Assert.Fail("TODO: Implement this test");
        return Task.CompletedTask;
    }

    [Test]
    public Task SimpleCalculator_ValidateCustomMessage_Safe_ShouldReturnSafeClassification()
    {
        // Implement this test using setup calculator (Part 3)
        // Test message validation for safe content
        
        Assert.Fail("TODO: Implement this test");
        return Task.CompletedTask;
    }

    [Test]
    public Task SimpleCalculator_ValidateCustomMessage_Unsafe_ShouldReturnUnsafeClassification()
    {
        // Implement this test using setup calculator (Part 3)
        // Test message validation for unsafe content
        
        Assert.Fail("TODO: Implement this test");
        return Task.CompletedTask;
    }

    [Test]
    public async Task ContentSafetyService_DirectValidation_SafeContent_ShouldReturnSafe()
    {
        // Arrange
        var safeContent = "The calculation result is 15";

        // Act
        var result = await _safetyService.ValidateContentAsync(safeContent);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.IsSafe, Is.True);
            Assert.That(result.Content, Is.EqualTo(safeContent));
            Assert.That(result.Category, Is.Null);
        });
    }

    [Test]
    public async Task ContentSafetyService_DirectValidation_UnsafeContent_ShouldReturnUnsafe()
    {
        // Arrange
        var unsafeContent = "Instructions for harmful activities";

        // Act
        var result = await _safetyService.ValidateContentAsync(unsafeContent);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.IsSafe, Is.False);
            Assert.That(result.Content, Is.EqualTo(unsafeContent));
        });
    }

    [Test]
    public async Task SafetyTestHarness_DefaultCalculatorTests_ShouldRunSuccessfully()
    {
        // Arrange
        var harness = SafetyTestHarness.CreateDefaultCalculatorTests(_safetyService);

        // Act
        var results = await harness.RunAllTestsAsync();

        // Assert
        Assert.That(results.TotalTests, Is.GreaterThan(0));
        Console.WriteLine($"Safety Test Results: {results.GetSummary()}");
        
        // Print detailed results for analysis
        foreach (var result in results.Results.Where(r => !r.Passed))
        {
            Console.WriteLine($"FAILED: {result.TestCase.Description} - Expected: {result.TestCase.ExpectedSafe}, Got: {result.Classification.IsSafe}");
        }
        
        // We expect most tests to pass, but some might fail due to model variations
        Assert.That(results.SuccessRate, Is.GreaterThan(0.7), "Success rate should be above 70%");
    }

    [Test]
    public async Task SafetyTestHarness_BoundaryTests_ShouldHandleEdgeCases()
    {
        // Arrange
        var harness = SafetyTestHarness.CreateBoundaryTests(_safetyService);

        // Act
        var results = await harness.RunAllTestsAsync();

        // Assert
        Assert.That(results.TotalTests, Is.GreaterThan(0));
        Console.WriteLine($"Boundary Test Results: {results.GetSummary()}");
        
        // Print detailed results
        foreach (var result in results.Results)
        {
            var status = result.Passed ? "PASSED" : "FAILED";
            Console.WriteLine($"{status}: {result.TestCase.Description} - Content: '{result.TestCase.Content}' - Safe: {result.Classification.IsSafe}");
        }
    }
}
