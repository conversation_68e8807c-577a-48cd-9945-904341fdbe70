# Exercise 2: NUnit Setup and Teardown with Content Safety Integration

## Overview

This exercise builds on Exercise 1 by introducing NUnit's setup and teardown mechanisms while maintaining the content safety integration. Students will learn to use `[SetUp]` and `[TearDown]` attributes to manage test state, reduce code duplication, and understand test isolation principles with both basic calculator operations and AI-powered content safety validation.

## Prerequisites

### Required Software
- .NET 8.0 SDK or later
- Visual Studio Code or Visual Studio
- Ollama (for content safety validation)
- Git
- Completed Exercise 1

### Ollama Setup
1. **Install Ollama**: Download and install from [https://ollama.ai](https://ollama.ai)
2. **Install Llama Guard Model**:
   ```bash
   ollama pull llama-guard3:8b
   ```
3. **Verify Installation**:
   ```bash
   ollama list
   # Should show llama-guard3:8b in the list
   ```
4. **Start Ollama Service**:
   ```bash
   ollama serve
   # Keep this running in a separate terminal
   ```

## Getting Started

### 1. Project Structure

The Exercise 2 structure includes:
- `Exercise2Tests/` - Unit tests with setup/teardown patterns
- `Exercise2SafetyDemo/` - Demo application showing expected functionality
- `Calculators/` - Shared calculator library (same as Exercise 1)

### 2. Verify Project Setup

Run the following command to ensure everything is working:

```bash
dotnet test --filter "ClassName=Exercise2Tests"
```

You should see tests fail (marked as TODO for students to implement).

### 3. Run the Demo Application

```bash
dotnet run --project Exercise2SafetyDemo
```

This will show you the setup/teardown pattern and expected functionality.

## Learning Objectives

1. **NUnit Setup and Teardown**
   - Understand `[SetUp]` and `[TearDown]` attributes
   - Learn the difference between method-level and class-level setup/teardown
   - Implement proper test state management

2. **Test Organization**
   - Reduce code duplication across tests
   - Manage shared test objects and state
   - Understand test isolation principles

3. **Debugging and Execution Order**
   - Use debugger to observe test execution flow
   - Understand when setup and teardown methods are called

## Exercise Structure

### Part 1: Basic Setup and Teardown (Steps 1-5)

#### Step 1: Write the Setup Method

**Objective**: Create a setup method that initializes a shared calculator with the number 4.

**Location**: `Exercise2Tests/Exercise2Tests.cs`

**Task**: Add a setup method to the test class so that:
- All tests use the same `SimpleCalculator` object
- The `SimpleCalculator` object starts with the number 4

**Implementation**:
```csharp
[SetUp]
public void Setup()
{
    _calculator = new SimpleCalculator();
    _calculator.Enter(4);
}
```

**Key Points**:
- Use the `[SetUp]` attribute
- The method runs before each individual test
- Initialize the `_calculator` field that's already declared in the class

### Step 2: Write the Teardown Method

**Objective**: Create a teardown method that resets the calculator after each test.

**Task**: Add a teardown method to ensure the calculator is reset after each test.

**Implementation**:
```csharp
[TearDown]
public void TearDown()
{
    _calculator.Reset();
}
```

**Key Points**:
- Use the `[TearDown]` attribute
- The method runs after each individual test
- Ensures test isolation by resetting state

### Step 3: Edit the Addition Test

**Objective**: Modify the addition test to use the setup calculator.

**Current Implementation**:
```csharp
[Test]
public void AdditionTest()
{
    var calculator = new SimpleCalculator();
    var result = calculator.Enter(4).Plus(2).Equals();
    Assert.That(result, Is.EqualTo(6));
}
```

**Modified Implementation**:
```csharp
[Test]
public void AdditionTest()
{
    // Arrange - calculator already has 4 from setup
    
    // Act
    var result = _calculator.Plus(2).Equals();
    
    // Assert
    Assert.That(result, Is.EqualTo(6));
}
```

**Key Changes**:
- Remove local calculator creation
- Use `_calculator` from setup
- Calculator already contains 4, so just add 2

### Step 4: Run and Debug Tests

**Objective**: Verify the setup/teardown execution order.

**Commands**:
```bash
# Run all Exercise 2 tests
dotnet test --filter "ClassName=Exercise2Tests"

# Run with detailed output
dotnet test --filter "ClassName=Exercise2Tests" --logger "console;verbosity=detailed"
```

**Debugging Tips**:
- Set breakpoints in Setup, TearDown, and test methods
- Observe the execution order: Setup → Test → TearDown
- Verify the calculator state at each step

### Step 5: Update All Tests

**Objective**: Modify all remaining tests to use the shared calculator from setup.

**Tasks**:
1. **SubtractionTest**: Calculator has 4, subtract 2, expect 2
2. **MultiplicationTest**: Calculator has 4, multiply by 2, expect 8  
3. **DivisionTest**: Calculator has 4, divide by 2, expect 2

**Pattern for Each Test**:
```csharp
[Test]
public void SubtractionTest()
{
    // Arrange - calculator already has 4 from setup
    
    // Act
    var result = _calculator.Minus(2).Equals();
    
    // Assert
    Assert.That(result, Is.EqualTo(2));
}
```

**Important**: Modify and verify each test one at a time before moving to the next.

### Part 2: Content Safety Integration with Setup/Teardown

**Objective**: Add AI-powered content safety validation using setup/teardown patterns.

**Location**: `Exercise2Tests/SafetyIntegrationTests.cs`

**Tasks**:

1. **Implement Safety Setup Method**:
   ```csharp
   [SetUp]
   public void Setup()
   {
       _httpClient = new HttpClient();
       _safetyService = new OllamaContentSafetyService(_httpClient);
       _calculator = new SimpleCalculator(_safetyService);
       _calculator.Enter(4);
   }
   ```

2. **Implement Safety Teardown Method**:
   ```csharp
   [TearDown]
   public void TearDown()
   {
       _calculator.Reset();
       _httpClient?.Dispose();
   }
   ```

3. **Update SimpleCalculator for Safety** (if not done in Exercise 1):
   - Uncomment the using statement: `using Calculators.ContentSafety;`
   - Add safety fields and constructor
   - Implement `EqualsAsync()`, `GetSafetyLog()`, and `ValidateMessageAsync()` methods

### Part 3: Safety Integration Tests with Setup/Teardown

**Objective**: Implement safety tests using the setup/teardown calculator.

**Tasks**: Implement the skeleton tests in `SafetyIntegrationTests.cs`:

1. **Basic Safety Test**: Test calculator with safety validation using setup calculator
2. **Multiple Operations Test**: Test safety logging across multiple calculations with reset
3. **Message Validation Tests**: Test direct message validation using setup calculator

## NUnit Setup/Teardown Attributes

### Method-Level Attributes
- **`[SetUp]`**: Runs before each test method
- **`[TearDown]`**: Runs after each test method
- **Use Case**: Initialize/cleanup resources needed by individual tests

### Class-Level Attributes
- **`[OneTimeSetUp]`**: Runs once before all tests in the class
- **`[OneTimeTearDown]`**: Runs once after all tests in the class
- **Use Case**: Expensive setup/cleanup operations (database connections, file I/O)

### When to Use Each Type

**Method-Level (`[SetUp]`/`[TearDown]`)**:
- Creating fresh objects for each test
- Resetting state between tests
- Ensuring test isolation
- Most common scenario

**Class-Level (`[OneTimeSetUp]`/`[OneTimeTearDown]`)**:
- Database connections
- File system setup
- Network connections
- Expensive initialization that can be shared

## Running Tests

### Run All Exercise 2 Tests
```bash
dotnet test --filter "Exercise2Tests"
```

### Run Specific Test Categories
```bash
# Basic calculator tests with setup/teardown
dotnet test --filter "ClassName=Exercise2Tests.Exercise2Tests"

# Safety integration tests with setup/teardown
dotnet test --filter "ClassName=Exercise2Tests.SafetyIntegrationTests"
```

### Run Demo Application
```bash
dotnet run --project Exercise2SafetyDemo
```

### Run with Debugging Information
```bash
dotnet test --filter "Exercise2Tests" --logger "console;verbosity=detailed"
```

## Success Criteria

### Part 1: Basic Setup/Teardown
✅ Setup method properly initializes calculator with 4
✅ Teardown method resets calculator after each test
✅ Addition test uses shared calculator from setup
✅ All basic tests modified to use shared calculator
✅ All basic tests pass when run together
✅ Understanding of setup/teardown execution order demonstrated

### Part 2: Safety Integration
✅ Safety setup method initializes calculator with safety service
✅ Safety teardown method properly cleans up resources
✅ Content safety integration implemented in SimpleCalculator
✅ Safety integration tests pass
✅ Demo application runs successfully

### Part 3: Advanced Understanding
✅ Understanding of test isolation with setup/teardown
✅ Knowledge of different NUnit setup/teardown attributes
✅ Ability to debug and trace test execution order
✅ Integration of async operations with setup/teardown patterns

## Common Issues and Solutions

### Issue: Tests Fail When Run Together
**Cause**: State not properly reset between tests  
**Solution**: Ensure teardown method properly resets calculator state

### Issue: Calculator Doesn't Have Expected Initial Value
**Cause**: Setup method not properly initializing calculator  
**Solution**: Verify setup method calls `_calculator.Enter(4)`

### Issue: NullReferenceException
**Cause**: Calculator not initialized in setup  
**Solution**: Ensure setup method creates new calculator instance

## Next Steps

After completing this exercise, you will understand:
- How to use NUnit setup and teardown methods
- The importance of test isolation
- When to use different types of setup/teardown attributes
- How to reduce code duplication in tests

This foundation will be essential for more complex testing scenarios in future exercises.
