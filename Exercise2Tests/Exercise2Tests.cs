using Calculators;

namespace Exercise2Tests;

public class Exercise2Tests
{
    private SimpleCalculator _calculator;

    // Add Setup method here (Step 1)
    // Use appropriate NUnit attribute
    // Initialize _calculator with number 4

    // Add Teardown method here (Step 2)  
    // Use appropriate NUnit attribute
    // Reset the calculator after each test

    [Test]
    public void AdditionTest()
    {
        // Step 3: Modify this test to use the setup calculator
        // The calculator should already have 4, just add 2 and expect 6
        
        // Current implementation (to be modified):
        var calculator = new SimpleCalculator();
        var result = calculator.Enter(4).Plus(2).Equals();
        Assert.That(result, Is.EqualTo(6));
    }

    [Test]
    public void SubtractionTest()
    {
        // Step 5: Modify this test to use the setup calculator
        // The calculator should already have 4, subtract 2 and expect 2
        
        var calculator = new SimpleCalculator();
        var result = calculator.Enter(4).Minus(2).Equals();
        Assert.That(result, Is.EqualTo(2));
    }

    [Test]
    public void MultiplicationTest()
    {
        // Step 5: Modify this test to use the setup calculator
        // The calculator should already have 4, multiply by 2 and expect 8
        
        var calculator = new SimpleCalculator();
        var result = calculator.Enter(4).MultiplyBy(2).Equals();
        Assert.That(result, Is.EqualTo(8));
    }

    [Test]
    public void DivisionTest()
    {
        // Step 5: Modify this test to use the setup calculator
        // The calculator should already have 4, divide by 2 and expect 2
        
        var calculator = new SimpleCalculator();
        var result = calculator.Enter(4).DivideBy(2).Equals();
        Assert.That(result, Is.EqualTo(2));
    }
}
