You will also need NUnit for writing and running the unit tests.

Introducing the code to test
For this exercise, we will test a simple calculator. Then, we will write the tests for the calculator.

Create a new .NET solution
We will create a new directory for this exercise from the terminal.

$ cd ~                                                    
$ mkdir TestStar                                          
$ cd TestStar                                             
$ mkdir IntroToTestFrameworks                             
$ cd IntroToTestFrameworks                                
$ code .                                                  
Change to your home directory. You can choose any other directory as you prefer.
Create a directory for TestStar exercises.
Change to the TestStar directory.
Create a new directory for this exercise - an introduction to test frameworks.
Change to the exercise directory.
Open VS Code from this directory.
In VSCode, you can open a terminal from the Terminal  New Terminal. You can also open a terminal in VSCode with Ctrl+Shift+`.

Open a terminal and enter the following commands to create a new .NET solution.

$ dotnet new sln                                          
The template "Solution File" was created successfully.
Create a new .NET solution. We need to create a solution because it will have two projects in it: (a) the Calculator code to test, and (b) the tests for the Calculator.
Add the code project that will be tested
In the terminal, run the following commands. Make sure that you are in the directory that has the solution file.

$ dotnet new classlib -o Calculators                       
The template "Class Library" was created successfully.
...
$ dotnet sln add Calculators/Calculators.csproj             
Project `Calculators/Calculators.csproj` added to the solution.
Create a new project for the Calculator code. The project is a simple class library. Imagine it is a component to be used in a bigger codebase. In our case, the unit to be tested is the Calculator class in the library.
Add the newly created project to the solution.
Add a project for the tests
In your terminal, create a new project for the unit tests, have it reference the code to test, and add it to the solution.

$ dotnet new nunit -o Exercise1Tests                              
The template "NUnit 3 Test Project" was created successfully.
...
$ dotnet sln add ./Exercise1Tests/Exercise1Tests.csproj         
Project `Exercise1Tests/Exercise1Tests.csproj` added to the solution.

$ dotnet add Exercise1Tests/Exercise1Tests.csprof reference Calculators/Calculators.csproj  
Reference `..\Calculators\Calculators.csproj` added to the project.
Create a new nunit project. This will ensure that the project includes a reference to the NUnit test framework, the NUnit test adaptor and the Microsoft test SDK.
Add the test project to the solution.
Add the Calculators project as a reference to the test project. Remember that we will write test code for the Calculators class. Therefore, the tests must be able to reference those classes.
Validating the solution and projects
When we ran the command dotnet new nunit …​, it generated a test in the Exercise1Tests/UnitTest1.cs file. This is a trivial test that does nothing, and just passes.

Let’s run the test to ensure that our solution and NUnit framework is correctly setup.

$ dotnet test                                                 
....
Starting test execution, please wait...
A total of 1 test files matched the specified pattern.

Passed!  - Failed:     0, Passed:     1, Skipped:     0, Total:     1, Duration: 16 ms
...
This command will try to detect all tests and execute them using the NUnit framework. You can run this command in the solution directory, or at the Exercise1Tests project directory.
Important	If you can’t get the above to work, then go back and double check how you created your solution and projects.
Add the code for the calculator
Now open Visual Studio Code (or the editor of your choice) if its not open. Then, open the solution directory that you created above. You can use the VS Code shortcut keys: Ctrl+K, Ctrl+O.

Navigate to Calculators directory and rename Class1.cs to SimpleCalculator.cs.

Replace the generated code in SimpleCalculator.cs with the following code.

Calculators/SimpleCalculator.cs
namespace Calculators
{
    public class SimpleCalculator
    {
        private decimal LastResult { get; set; }                        

        public SimpleCalculator()                                       
        {
            Reset();
        }

        public SimpleCalculator Reset()                                 
        {
            LastResult = 0.0M;
            return this;
        }

        public SimpleCalculator Enter(decimal number)                   
        {
            LastResult = number;
            return this;
        }

        public SimpleCalculator Plus(decimal number)                    
        {
            LastResult += number;
            return this;
        }

        public SimpleCalculator Minus(decimal number)                   
        {
            LastResult -= number;
            return this;
        }

        public SimpleCalculator MultiplyBy(decimal number)              
        {
            LastResult *= number;
            return this;
        }

        public SimpleCalculator DivideBy(decimal number)                
        {
            LastResult /= number;
            return this;
        }

        public decimal Equals()                                         
        {
            return LastResult;
        }
    }
}
The calculator keeps track of the output of the last calculation. The access is private because only the calculator object should be able to manage the value of LastResult.
A new calculator starts off in a reset state.
Resetting the calculator simply clears the LastResult, i.e. setting it to be zero.
This is used to enter a single number into the calculator. We return the object itself.This allows us to chain calculator operations using a fluent code style. For example, we are then able to write code such as calculator.Reset().Enter(10.0M).Plus(20.0M).Equals();.
Add a number to the last result. Again, we return the object itself so we can chain calls in fluid style.
Subtract a number from the last result.
Multiply the last result by the number provided.
Divide the last result by the number provided.
Return the last result. This operation returns the actual number and ends a chain of calculator operations.
The AAA way of writing tests
We can think of writing tests consistent of 3 parts.

Arrange
The Arrange part of the test prepares or creates the starting state of the test. It sets the initial state or context for the test.

Act
The Act part calls an action / method on the tested object and captures the output result in a varible. This is the action of the test.

Assert
The Assert part verifies the result to match what we expected. This is the final state or context of the test.

Write our first automated test
We will use the AAA way to write our first test for the calculator.

Rename Exercise1Tests/UnitTest1.cs to Exercise1Tests.cs. Then, add the following code to the file.

Exercise1Tests/Exercise1Tests.cs
using Calculators;

namespace Exercise1Tests;

public class Exercise1Tests
{
    [Test]
    public void AdditionTest()
    {
        // Arrange
        var calculator = new SimpleCalculator();                    
            
        // Act
        var result = calculator.Enter(4).Plus(2).Equals();          
            
        // Assert
        Assert.That(result, Is.EqualTo(6));                         
    }
}
Our arrange part simply creates a SimpleCalculator object.
The act part first enters the number 4 into the calculator, then it adds 2, and gets the answer
Finally, the assert part verifies that the result is equal to 6 (4+2=6).
Run the test and it should pass.

$ dotnet test
...
Failed!  - Failed:     0, Passed:     1, Skipped:     0, Total:     1, Duration: 29 ms
Exercise 1
Using the AAA way of structuring tests, write tests for the following Calculator functionality.

1. Subtraction
Write a tests that enters the number 4 into the calculator, then subtracts 2.

2. Multiplication
Write a tests that enters the number 4 into the calculator, then multiplies it by 2.

3. Division
Write a tests that enters the number 4 into the calculator, then divides it by 2.

## Enhanced Exercise: Content Safety Integration with Llama Guard

This exercise has been enhanced to introduce the concept of validating model or system outputs for harmful or unsafe content using Llama Guard through Ollama.

### Prerequisites

- Ollama installed and running locally
- Llama Guard model available (`llama-guard3:8b`)

### New Features Added

#### 1. Content Safety Architecture

The solution now includes a comprehensive content safety validation system:

- **SafetyClassification**: Represents the result of content safety validation
- **IContentSafetyService**: Interface for content safety validation services
- **OllamaContentSafetyService**: Implementation that integrates with Ollama and Llama Guard
- **UnsafeContentException**: Exception thrown when unsafe content is detected

#### 2. Enhanced Calculator with Safety Validation

- **SafeCalculator**: An enhanced calculator that validates all outputs for content safety
- Automatically routes calculation results through Llama Guard for classification
- Maintains a safety log of all validations performed
- Throws exceptions when unsafe content is detected

#### 3. Comprehensive Test Harness

- **SafetyTestHarness**: A test harness that routes sample outputs through Llama Guard
- Classifies responses as "safe" or "unsafe"
- Includes default test cases for calculator outputs
- Supports boundary testing for edge cases
- Provides detailed reporting of test results

### Running the Enhanced Tests

#### Basic Calculator Tests (Original)
```bash
dotnet test --filter "Name~Addition|Name~Subtraction|Name~Multiplication|Name~Division"
```

#### Content Safety Integration Tests
```bash
# Test direct content validation
dotnet test --filter "Name~ContentSafetyService"

# Test safe calculator integration
dotnet test --filter "Name~SafeCalculator"

# Test the comprehensive test harness
dotnet test --filter "Name~SafetyTestHarness"
```

#### Run All Tests
```bash
dotnet test
```

### Example Usage

#### Using the Safe Calculator
```csharp
using var httpClient = new HttpClient();
var safetyService = new OllamaContentSafetyService(httpClient);
var calculator = new SafeCalculator(safetyService);

// Safe calculation
var result = await calculator.Enter(4).Plus(2).EqualsAsync(); // Returns 6

// View safety log
var safetyLog = calculator.GetSafetyLog();
Console.WriteLine($"Performed {safetyLog.Count} safety validations");
```

#### Using the Test Harness
```csharp
var harness = SafetyTestHarness.CreateDefaultCalculatorTests(safetyService);
var results = await harness.RunAllTestsAsync();
Console.WriteLine($"Test Results: {results.GetSummary()}");
```

### Key Learning Objectives

1. **Test-Driven Development (TDD)**: Continue practicing TDD with the original calculator tests
2. **Unit Testing with NUnit**: Learn advanced testing patterns with async operations
3. **Content Safety Validation**: Understand how to integrate AI-powered content moderation
4. **Integration Testing**: Test the interaction between multiple system components
5. **Test Harness Development**: Build comprehensive testing frameworks for validation

### Architecture Benefits

- **Separation of Concerns**: Safety validation is cleanly separated from business logic
- **Testability**: All components are fully testable with comprehensive test coverage
- **Extensibility**: Easy to add new safety validation providers or enhance existing ones
- **Observability**: Complete logging and monitoring of safety validations
- **Security-First**: Defaults to "unsafe" in case of errors or unclear responses