# Exercise 1: Calculator with Content Safety Integration

## Overview

This exercise introduces Test-Driven Development (TDD) with NUnit and integrates AI-powered content safety validation using Llama Guard through Ollama. Students will learn to write unit tests, implement calculator functionality, and add content safety features.

## Prerequisites

### Required Software
- .NET 8.0 SDK or later
- Visual Studio Code or Visual Studio
- Ollama (for content safety validation)
- Git

### Ollama Setup
1. **Install Ollama**: Download and install from [https://ollama.ai](https://ollama.ai)
2. **Install Llama Guard Model**: 
   ```bash
   ollama pull llama-guard3:8b
   ```
3. **Verify Installation**:
   ```bash
   ollama list
   # Should show llama-guard3:8b in the list
   ```
4. **Start Ollama Service**:
   ```bash
   ollama serve
   # Keep this running in a separate terminal
   ```

## Getting Started

### 1. Clone and Setup the Project

The project structure is already set up with:
- `Calculators/` - Main calculator library
- `Exercise1Tests/` - Unit tests for basic calculator functionality
- `SafetyDemo/` - Demo application showing content safety integration

### 2. Verify Project Setup

Run the following command to ensure everything is working:

```bash
dotnet test
```

You should see some tests pass and some fail (marked as TODO for students to implement).

### 3. Run the Demo Application

```bash
dotnet run --project SafetyDemo
```

This will show you what the final implementation should look like.

## Exercise Structure

### Part 1: Basic Calculator Tests (TDD Fundamentals)

**Objective**: Learn Test-Driven Development by implementing basic calculator tests.

**Location**: `Exercise1Tests/Exercise1Tests.cs`

**Tasks**:
1. **Subtraction Test**: Implement the `SubtractionTest()` method
   - Enter 4, subtract 2, expect result 2
2. **Multiplication Test**: Implement the `MultiplicationTest()` method  
   - Enter 4, multiply by 2, expect result 8
3. **Division Test**: Implement the `DivisionTest()` method
   - Enter 4, divide by 2, expect result 2

**The AAA Pattern**:
- **Arrange**: Set up test data and objects
- **Act**: Execute the method being tested
- **Assert**: Verify the expected outcome

**Example**:
```csharp
[Test]
public void SubtractionTest()
{
    // Arrange
    var calculator = new SimpleCalculator();

    // Act
    var result = calculator.Enter(4).Minus(2).Equals();

    // Assert
    Assert.That(result, Is.EqualTo(2));
}
```

### Part 2: Content Safety Integration

**Objective**: Add AI-powered content safety validation to the calculator.

**Location**: `Calculators/SimpleCalculator.cs`

**Tasks**:

1. **Uncomment the using statement**:
   ```csharp
   using Calculators.ContentSafety;
   ```

2. **Add Safety Fields**:
   ```csharp
   private readonly IContentSafetyService _safetyService;
   private readonly List<SafetyClassification> _safetyLog;
   ```

3. **Implement Safety Constructor**:
   ```csharp
   public SimpleCalculator(IContentSafetyService safetyService)
   {
       _safetyService = safetyService;
       _safetyLog = new List<SafetyClassification>();
   }
   ```

4. **Add Async Equals Method**:
   ```csharp
   public async Task<decimal> EqualsAsync()
   {
       var result = Equals();
       var resultText = $"The calculation result is {result}";

       var safetyResult = await _safetyService.ValidateContentAsync(resultText);
       _safetyLog.Add(safetyResult);

       if (!safetyResult.IsSafe)
       {
           throw new UnsafeContentException($"Content validation failed: {safetyResult.Category}", safetyResult);
       }

       return result;
   }
   ```

5. **Add Safety Log Access**:
   ```csharp
   public IReadOnlyList<SafetyClassification> GetSafetyLog()
   {
       return _safetyLog.AsReadOnly();
   }
   ```

6. **Add Message Validation**:
   ```csharp
   public async Task<SafetyClassification> ValidateMessageAsync(string message)
   {
       var result = await _safetyService.ValidateContentAsync(message);
       _safetyLog.Add(result);
       return result;
   }
   ```

### Part 3: Safety Integration Tests

**Objective**: Test the content safety integration.

**Location**: `Exercise1Tests/SafetyIntegrationTests.cs`

**Tasks**: Implement the skeleton tests marked with `TODO` comments:

1. **Basic Safety Test**: Test calculator with safety validation
2. **Multiple Operations Test**: Test safety logging across multiple calculations  
3. **Message Validation Tests**: Test direct message validation for safe/unsafe content

## Running Tests

### Run All Tests
```bash
dotnet test
```

### Run Specific Test Categories
```bash
# Basic calculator tests only
dotnet test --filter "ClassName=Exercise1Tests"

# Safety integration tests only  
dotnet test --filter "ClassName=SafetyIntegrationTests"
```

### Run Demo Application
```bash
dotnet run --project SafetyDemo
```

## Key Learning Objectives

1. **Test-Driven Development (TDD)**
   - Write tests first, then implement functionality
   - Use the AAA pattern (Arrange, Act, Assert)
   - Understand red-green-refactor cycle

2. **Unit Testing with NUnit**
   - Write effective unit tests
   - Use NUnit assertions and attributes
   - Test async operations

3. **Content Safety Integration**
   - Integrate AI-powered content moderation
   - Handle async operations in tests
   - Implement proper error handling

4. **Software Architecture**
   - Separation of concerns
   - Dependency injection patterns
   - Interface-based design

## Troubleshooting

### Ollama Issues
- **Connection Error**: Ensure Ollama is running (`ollama serve`)
- **Model Not Found**: Run `ollama pull llama-guard3:8b`
- **Port Issues**: Ollama runs on port 11434 by default

### Test Issues
- **Compilation Errors**: Uncomment the `using Calculators.ContentSafety;` line
- **Missing Methods**: Implement the TODO methods in SimpleCalculator
- **Async Test Issues**: Use `async Task` for test methods that call async code

### Build Issues
```bash
# Clean and rebuild
dotnet clean
dotnet build
```

## Success Criteria

✅ All basic calculator tests pass  
✅ Content safety integration implemented  
✅ Safety integration tests pass  
✅ Demo application runs successfully  
✅ Understanding of TDD principles demonstrated

## Next Steps

After completing this exercise, you will have learned:
- How to write effective unit tests using TDD
- How to integrate AI-powered content safety validation
- How to work with async operations in tests
- How to structure code for testability and maintainability

Continue practicing these concepts in your own projects!
